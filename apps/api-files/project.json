{"name": "api-files", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/api-files/src", "projectType": "application", "targets": {"build": {"defaultConfiguration": "production", "executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "options": {"sourceMap": true, "assets": [{"glob": "**/*", "input": "libs/crossplatform/api-files/openapi", "output": "assets"}], "compiler": "tsc", "main": "apps/api-files/src/main.ts", "outputPath": "dist/apps/api-files", "target": "node", "tsConfig": "apps/api-files/tsconfig.app.json", "webpackConfig": "apps/api-files/webpack.config.js"}, "configurations": {"production": {"generatePackageJson": false, "optimization": true, "extractLicenses": true, "inspect": false, "production": true}}}, "serve": {"executor": "@nx/js:node", "options": {"buildTarget": "api-files:build"}, "configurations": {"production": {"buildTarget": "api-files:build:production"}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/apps/api-files"], "cache": true, "dependsOn": ["prisma-codegen", "^prisma-codegen", {"projects": ["backend-shared-prisma-infra"], "target": "prisma-setup-testing"}], "inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "options": {"jestConfig": "apps/api-files/jest.config.ts", "testPathIgnorePatterns": [".*\\.scheduled\\.spec\\.ts"], "testPathPattern": [".*\\.spec\\.ts"]}}, "deploy-docker-image": {"executor": "nx:run-commands", "options": {"commands": ["tools/docker-build-and-push.sh apps/api-files"], "parallel": false}, "dependsOn": ["build"]}}, "tags": ["platform:backend", "scope:api-files", "type:app"]}