import './parametersOrganizationReceivershipRegisterItem.scss';
import { AnswerDict } from '@mynotary/crossplatform/records/api';
import { classNames, useParams } from '@mynotary/frontend/shared/util';
import React, { ReactElement, useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { MnButtonDropdown, MnButtonReturn, MnSidePopin } from '@mynotary/frontend/shared/ui';
import { Form } from '@mynotary/frontend/legals/feature';
import { isEmpty, keyBy, map, unset } from 'lodash';
import { ParametersOrganizationReceivershipRegisterItemForm } from './form/parametersOrganizationReceivershipRegisterItemForm';
import { ParametersOrganizationReceivershipRegisterItemReceipt } from './receipt/parametersOrganizationReceivershipRegisterItemReceipt';
import { ReceiptType } from '../receivership';
import { formatPrice } from '@mynotary/crossplatform/shared/util';
import { routePaths } from '@mynotary/frontend/routes/api';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { getRegisterEntry, selectRegisterEntryOrThrow, updateRegisterEntry } from '@mynotary/frontend/registers/store';
import { generateReceivershipEntryBalance, newEntryFormByType } from '@mynotary/frontend/registers/core';
import { useNavigate } from 'react-router';

const ParametersOrganizationReceivershipRegisterItem = (): ReactElement => {
  const params = useParams();
  const navigate = useNavigate();
  const dispatch = useAsyncDispatch();
  const entryId = parseInt(params['id'] ?? '');

  // TODO selectRegisterEntryOrThrow
  const entry = useSelector(selectRegisterEntryOrThrow(entryId, 'RECEIVERSHIP'));
  const [creationType, setCreationType] = useState<ReceiptType | null>((params['type'] as ReceiptType) ?? null);
  const [selectedReceipt, setSelectedReceipt] = useState<AnswerDict>();
  const [preview, setPreview] = useState(false);
  const receipts = useMemo(() => {
    return entry?.answer['receipts']?.value as AnswerDict[];
  }, [entry]);

  useEffect(() => {
    dispatch(getRegisterEntry(entryId));
  }, [dispatch, entryId]);

  const actions = [
    {
      icon: '/assets/images/pictos/icon/deposit.svg',
      id: 'deposit',
      label: 'Réception de fonds',
      onClick: () => {
        setCreationType('DEPOSIT');
      }
    },
    {
      icon: '/assets/images/pictos/icon/refund.svg',
      id: 'refund',
      label: 'Restitution de fonds',
      onClick: () => {
        setCreationType('REFUND');
      }
    }
  ];

  const handleClosePopin = (): void => {
    setCreationType(null);
    setSelectedReceipt(undefined);
    setPreview(false);
  };

  const handleReceiptCreation = (answer: AnswerDict): void => {
    handleClosePopin();
    if (!entry) {
      return;
    }
    dispatch(
      updateRegisterEntry({
        answer: {
          ...entry.answer,
          receipts: {
            value: {
              ...keyBy(receipts ? receipts : [], (receipt) => receipt['numero_recu'].value),
              [answer['numero_recu'].value]: answer
            }
          }
        },
        id: entry.id
      })
    );
  };

  const handleDeleteReceipt = (receipt: AnswerDict): void => {
    if (!entry) {
      return;
    }
    const receiptsUpdate = { ...entry.answer['receipts'].value };
    unset(receiptsUpdate, receipt['numero_recu'].value);

    dispatch(
      updateRegisterEntry({
        answer: {
          ...entry.answer,
          receipts: {
            value: { ...receiptsUpdate }
          }
        },
        id: entry.id
      })
    );
  };

  const handleModifyReceipt = (receipt: AnswerDict): void => {
    setCreationType(receipt['type_recu'].value);
    setSelectedReceipt(receipt);
  };

  const handlePreviewReceipt = (receipt: AnswerDict): void => {
    setCreationType(receipt['type_recu'].value);
    setSelectedReceipt(receipt);
    setPreview(true);
  };

  return (
    <>
      <div className={classNames('parameters-organization-receivership-register-item')}>
        <MnButtonReturn
          className='porri-return'
          onClick={() => navigate(routePaths.parameters.organization.register.receivership.table.path)}
        />
        <h3 className='porri-title'>Compte séquestre n°{entry?.answer?.['numero_registre']?.value}</h3>
        <h5 className='porri-sub-title'>Balance : {formatPrice(generateReceivershipEntryBalance(entry))}</h5>
        <div className='porri-content-container'>
          <div className='porri-form'>
            {entry && <Form answer={entry.answer} editable={false} forms={newEntryFormByType.RECEIVERSHIP} />}
          </div>
          <div className='porri-list'>
            <MnButtonDropdown
              actions={actions}
              className='porri-create-button'
              icon='/assets/images/pictos/outlined/arrow-12px.svg'
              label='Nouveau reçu'
            />
            {!isEmpty(receipts) &&
              map(receipts, (receipt, index) => (
                <ParametersOrganizationReceivershipRegisterItemReceipt
                  entry={entry}
                  key={index}
                  onDelete={handleDeleteReceipt}
                  onModify={handleModifyReceipt}
                  onPreview={handlePreviewReceipt}
                  receipt={receipt}
                />
              ))}
            {isEmpty(receipts) && <div className='porri-empty-list'>Aucun reçu</div>}
          </div>
        </div>
      </div>
      <MnSidePopin isOpen={!!creationType} onClose={handleClosePopin}>
        {creationType && (
          <ParametersOrganizationReceivershipRegisterItemForm
            className='porri-add-popin'
            entry={entry}
            onCreate={handleReceiptCreation}
            preview={preview}
            receipt={selectedReceipt}
            type={creationType}
          />
        )}
      </MnSidePopin>
    </>
  );
};

export { ParametersOrganizationReceivershipRegisterItem };
