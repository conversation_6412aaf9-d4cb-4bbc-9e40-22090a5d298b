{"name": "api-auth", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/api-auth/src", "projectType": "application", "targets": {"build": {"defaultConfiguration": "production", "executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/api-auth", "main": "apps/api-auth/src/main.ts", "tsConfig": "apps/api-auth/tsconfig.app.json", "assets": [{"glob": "**/*", "input": "libs/crossplatform/api-auth/openapi", "output": "assets"}], "target": "node", "compiler": "tsc", "webpackConfig": "apps/api-auth/webpack.config.js"}, "configurations": {"production": {"generatePackageJson": false, "optimization": true, "extractLicenses": true, "inspect": false, "production": true}}}, "serve": {"executor": "@nx/js:node", "options": {"buildTarget": "api-auth:build"}, "configurations": {"production": {"buildTarget": "api-auth:build:production"}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/apps/api-auth"], "cache": true, "dependsOn": ["prisma-codegen", "^prisma-codegen", {"projects": ["backend-shared-prisma-infra"], "target": "prisma-setup-testing"}], "inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "options": {"jestConfig": "apps/api-auth/jest.config.ts", "testPathIgnorePatterns": [".*\\.scheduled\\.spec\\.ts"], "testPathPattern": [".*\\.spec\\.ts"]}}, "deploy-docker-image": {"executor": "nx:run-commands", "options": {"commands": ["tools/docker-build-and-push.sh apps/api-auth"], "parallel": false}, "dependsOn": ["build"]}}, "tags": ["platform:backend", "scope:api-auth", "type:app"]}