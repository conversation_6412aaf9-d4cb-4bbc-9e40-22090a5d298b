{"name": "api-mynotary", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/api-mynotary/src", "projectType": "application", "targets": {"build": {"defaultConfiguration": "production", "executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "options": {"sourceMap": true, "assets": [{"glob": "**/*", "input": "libs/crossplatform/api-mynotary/openapi", "output": "assets"}], "compiler": "tsc", "main": "apps/api-mynotary/src/main.ts", "outputPath": "dist/apps/api-mynotary", "target": "node", "tsConfig": "apps/api-mynotary/tsconfig.app.json", "webpackConfig": "apps/api-mynotary/webpack.config.js"}, "configurations": {"production": {"generatePackageJson": false, "optimization": true, "extractLicenses": true, "inspect": false, "production": true}}}, "serve": {"executor": "@nx/js:node", "options": {"buildTarget": "api-mynotary:build"}, "configurations": {"production": {"buildTarget": "api-mynotary:build:production"}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/apps/api-mynotary"], "cache": true, "dependsOn": ["prisma-codegen", "^prisma-codegen", {"projects": ["backend-shared-prisma-infra"], "target": "prisma-setup-testing"}], "inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "options": {"jestConfig": "apps/api-mynotary/jest.config.ts", "testPathIgnorePatterns": [".*\\.scheduled\\.spec\\.ts"], "testPathPattern": [".*\\.spec\\.ts"]}}, "deploy-docker-image": {"executor": "nx:run-commands", "options": {"commands": ["tools/docker-build-and-push.sh apps/api-mynotary"], "parallel": false}, "dependsOn": ["build"]}}, "tags": ["platform:backend", "scope:api-mynotary", "type:app"]}