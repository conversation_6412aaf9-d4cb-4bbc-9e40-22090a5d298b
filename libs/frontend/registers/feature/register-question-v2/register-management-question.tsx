import React, { ReactElement } from 'react';
import { useSelector } from 'react-redux';
import { hasPermission } from '@mynotary/frontend/roles/api';
import { selectConnectedUserRole } from '@mynotary/frontend/roles/api';
import { EntityType, PermissionType } from '@mynotary/crossplatform/roles/api';
import { useFeatureState } from '@mynotary/frontend/features/api';
import { FeatureType } from '@mynotary/crossplatform/features/api';
import {
  selectCurrentManagementRegister,
  selectRegisterEntryByLegalOperationId
} from '@mynotary/frontend/registers/store';
import { MnProps } from '@mynotary/frontend/shared/util';
import { Answer, RegisterFormQuestion } from '@mynotary/crossplatform/shared/forms-util';
import { RegisterQuestion } from './register-question';
import { RegisterQuestionWithoutAction } from './register-question-without-action';
import { some } from 'lodash';
import { selectContract } from '@mynotary/frontend/legals/api';
import { selectCurrentContractId, selectCurrentOperationId } from '@mynotary/frontend/current-operation/api';

interface RegisterManagementQuestionProps extends MnProps {
  answer: Answer;
  className?: string;
  debounce: boolean;
  disabled: boolean;
  onChange?: (value: string | null) => void;
  question: RegisterFormQuestion;
}

export const RegisterManagementQuestion = (props: RegisterManagementQuestionProps): ReactElement => {
  const contractId = useSelector(selectCurrentContractId);
  const role = useSelector(selectConnectedUserRole);
  const operationId = useSelector(selectCurrentOperationId);
  const contract = useSelector(selectContract(contractId));
  const managementRegister = useSelector(selectCurrentManagementRegister);

  const registerEntry = useSelector(selectRegisterEntryByLegalOperationId(operationId, 'TRANSACTION'));
  if (registerEntry) {
    props.disabled = true;
  }
  const { isActive: hasManagementFeature } = useFeatureState(FeatureType.MANAGEMENT_REGISTER_ACCESS);
  const isAllowedContract = some(
    props.question.register.contracts,
    (contractType) => contractType === contract?.legalContractTemplateId
  );
  const isRegisterInitialized = managementRegister?.config != null;

  const hasRegisterEntryCreatePermission = hasPermission(
    PermissionType.CREATE_ORGANIZATION_REGISTER_ENTRY,
    role,
    EntityType.MANAGEMENT_REGISTER
  );

  const hasRegisterEntryManuallyCreatePermission =
    !isRegisterInitialized ||
    hasPermission(PermissionType.CREATE_ORGANIZATION_REGISTER_MANUALLY_ENTRY, role, EntityType.MANAGEMENT_REGISTER);

  if (hasManagementFeature && hasRegisterEntryCreatePermission && isAllowedContract && isRegisterInitialized) {
    return (
      <RegisterQuestion
        {...props}
        hasRegisterEntryManuallyCreatePermission={hasRegisterEntryManuallyCreatePermission}
      />
    );
  } else {
    return (
      <RegisterQuestionWithoutAction
        {...props}
        hasRegisterEntryCreatePermission={hasRegisterEntryCreatePermission}
        hasRegisterEntryManuallyCreatePermission={hasRegisterEntryManuallyCreatePermission}
        isAllowedContract={isAllowedContract}
        isRegisterInitialized={isRegisterInitialized}
      />
    );
  }
};
