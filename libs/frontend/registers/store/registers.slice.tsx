import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RegisterEntry } from '@mynotary/frontend/registers/core';

type Registers = {
  entries: RegisterEntry[];
};

const initialState: Registers = { entries: [] };

export const registersSlice = createSlice({
  initialState,
  name: 'registers',
  reducers: {
    addRegisterEntries: (state, action: PayloadAction<RegisterEntry[]>) => {
      state.entries = action.payload;
    }
  }
});

export interface RegisterState {
  [registersSlice.name]: Registers;
}

export const selectRegistersFeature = (state: RegisterState) => state[registersSlice.name];
