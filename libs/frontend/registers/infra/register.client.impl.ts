import {
  ManagementRegisterConfigDto,
  ManagementRegisterUpdateDto,
  ReceivershipRegisterConfigDto,
  ReceivershipRegisterUpdateDto,
  RegisterEntryListDto,
  TransactionRegisterConfigDto,
  TransactionRegisterUpdateDto
} from '@mynotary/crossplatform/api-mynotary/openapi';
import {
  ManagementRegister,
  ReceivershipRegister,
  RegistersClient,
  TransactionRegister,
  UpdateReceivershipRegisterArgs,
  UpdateTransactionRegisterArgs,
  UpdateManagementRegisterArgs,
  GetNextNumberArgs,
  RegisterEntry,
  GetRegisterEntriesArgs
} from '@mynotary/frontend/registers/core';
import { createAxiosInstance } from '@mynotary/frontend/shared/axios-util';
import { environment } from '@mynotary/frontend/shared/environments-util';
import { convertEnum } from '@mynotary/crossplatform/shared/util';

export class RegistersClientImpl implements RegistersClient {
  mainApiClient = createAxiosInstance({ baseURL: environment.apiMyNotaryUrl });
  javaApiClient = createAxiosInstance({ baseURL: environment.apiJavaUrl });

  async getManagementRegister(organizationId: number): Promise<ManagementRegister> {
    const { data } = await this.mainApiClient.get<ManagementRegisterConfigDto>('/management-registers', {
      params: { organizationId }
    });

    return convertDtoToManagementRegister(data);
  }

  async getReceivershipRegister(organizationId: number): Promise<ReceivershipRegister> {
    const { data } = await this.mainApiClient.get<ReceivershipRegisterConfigDto>('/receivership-registers', {
      params: { organizationId }
    });

    return convertDtoToReceivershipRegister(data);
  }

  async getTransactionRegister(organizationId: number): Promise<TransactionRegister> {
    const { data } = await this.mainApiClient.get<TransactionRegisterConfigDto>('/transaction-registers', {
      params: { organizationId }
    });

    return convertDtoToTransactionRegister(data);
  }

  async updateManagementRegister(args: UpdateManagementRegisterArgs): Promise<void> {
    await this.mainApiClient.put<void>(`/management-registers/${args.id}`, convertManagementRegisterUpdateToDto(args));
  }

  async updateReceivershipRegister(args: UpdateReceivershipRegisterArgs): Promise<void> {
    await this.mainApiClient.put<void>(
      `/receivership-registers/${args.id}`,
      convertReceivershipRegisterUpdateToDto(args)
    );
  }

  async updateTransactionRegister(args: UpdateTransactionRegisterArgs): Promise<void> {
    await this.mainApiClient.put<void>(
      `/transaction-registers/${args.id}`,
      convertTransactionRegisterUpdateToDto(args)
    );
  }

  async getNextRegisterNumber(args: GetNextNumberArgs): Promise<number | null> {
    const { data } = await this.javaApiClient.get<{ number: number }>(`/register/next-number/`, {
      params: { organizationId: args.organizationId, registerType: args.registerType }
    });

    if (data) {
      return data.number;
    }
    return null;
  }

  async getRegisterEntries(args: GetRegisterEntriesArgs): Promise<RegisterEntry[]> {
    console.log('SHERLOCK 1111', args);

    const params: any = {
      filterNumber: args.filterNumber,
      legalOperationId: args.legalOperationId,
      organizationId: args.organizationId,
      page: args.page ?? 1,
      pageSize: args.pageSize ?? 20,
      type: args.registerType
    };

    // Only include search parameter if it has a non-empty value
    if (args.search && args.search.trim() !== '') {
      params.search = encodeURIComponent(args.search);
    }

    const { data } = await this.mainApiClient.get<RegisterEntryListDto>('/register-entries', {
      params
    });
    console.log('SHERLOCK 2222');
    return data.items.map((item) => {
      return {
        ...item,
        creationTime: new Date(item.creationTime).getTime(),
        creator: {
          email: item.creatorEmail,
          firstname: item.creatorFirstname,
          id: parseInt(item.creatorId),
          lastname: item.creatorLastname
        },
        id: parseInt(item.id),
        operationId: parseInt(item.legalOperationId),
        status: convertEnum(RegisterEntryStatus, item.status),
        type: convertEnum(RegisterEntryType, item.type)
      };
    });
  }

  async getRegisterEntriesLegacy(args: GetRegisterEntriesArgs): Promise<RegisterEntry[]> {
    const params: any = {
      filterNumber: args.filterNumber,
      organizationId: args.organizationId,
      pageSize: args.pageSize,
      registerType: args.registerType
    };

    // Only include filter parameter if it has a non-empty value
    if (args.search && args.search.trim() !== '') {
      params.filter = encodeURIComponent(args.search);
    }

    const { data, status } = await this.javaApiClient.get<RegisterEntriesDto>(`/register/entries`, {
      params
    });

    /**
     * If the status is 204, it means that the server has successfully processed the request and that there is no content to send in the response.
     * The response is 200 when there are entries.
     */
    if (status === 204) {
      return [];
    }

    return data.entries;
  }
}

function convertDtoToTransactionRegister(dto: TransactionRegisterConfigDto): TransactionRegister {
  return { config: dto.config ?? null, id: parseInt(dto.id), organizationId: parseInt(dto.organizationId) };
}

function convertDtoToManagementRegister(dto: ManagementRegisterConfigDto): ManagementRegister {
  return { config: dto.config ?? null, id: parseInt(dto.id), organizationId: parseInt(dto.organizationId) };
}

function convertDtoToReceivershipRegister(dto: ReceivershipRegisterConfigDto): ReceivershipRegister {
  return { config: dto.config ?? null, id: parseInt(dto.id), organizationId: parseInt(dto.organizationId) };
}

function convertTransactionRegisterUpdateToDto(update: UpdateTransactionRegisterArgs): TransactionRegisterUpdateDto {
  return { ...update, organizationId: update.organizationId.toString() };
}

function convertReceivershipRegisterUpdateToDto(update: UpdateReceivershipRegisterArgs): ReceivershipRegisterUpdateDto {
  return { ...update, organizationId: update.organizationId.toString() };
}

function convertManagementRegisterUpdateToDto(update: UpdateManagementRegisterArgs): ManagementRegisterUpdateDto {
  return { ...update, organizationId: update.organizationId.toString() };
}

interface RegisterEntriesDto {
  entries: RegisterEntry[];
  total: number;
}

enum RegisterEntryType {
  MANAGEMENT = 'MANAGEMENT',
  RECEIVERSHIP = 'RECEIVERSHIP',
  TRANSACTION = 'TRANSACTION'
}

enum RegisterEntryStatus {
  CLOSED = 'CLOSED',
  RESERVED = 'RESERVED',
  VALIDATED = 'VALIDATED'
}
